# ThreatMesh Logging Configuration Guide

## Overview

This guide explains how to configure and troubleshoot logging in the ThreatMesh application, particularly for debug-level messages.

## Quick Fix Summary

The following changes have been made to enable debug logging:

1. **Enhanced logging format** in `app/main.py` and `app/worker.py`
2. **Uvicorn log level synchronization** with application log level
3. **Environment-based log level configuration** for all components

## Configuration

### Environment Variables

Set the `LOG_LEVEL` environment variable in your `.env` file:

```bash
# For debug logging (shows all messages)
LOG_LEVEL=DEBUG

# For info logging (default)
LOG_LEVEL=INFO

# For warnings and errors only
LOG_LEVEL=WARNING
```

### Testing Debug Logging

1. **Test the logging configuration:**
   ```bash
   python test_debug_logging.py
   ```

2. **Run the application with debug logging:**
   ```bash
   # Set LOG_LEVEL=DEBUG in your .env file first
   python run.py
   ```

3. **Check Docker logs:**
   ```bash
   # For local development
   docker-compose -f docker-compose-local.yml logs -f api

   # For production
   docker-compose logs -f api
   ```

## Log Format

The new log format includes:
- **Timestamp**: When the log message was created
- **Logger name**: Which module/file generated the message
- **Log level**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Message**: The actual log content

Example output:
```
2024-01-15 10:30:45 - app.routers.asm.webservers - DEBUG - Fetching chunks: [0, 1] for job_id: abc123
2024-01-15 10:30:45 - app.routers.asm.webservers - INFO - Starting webserver discovery for domain example.com
```

## Troubleshooting

### Debug Messages Not Appearing

1. **Check environment variable:**
   ```bash
   echo $LOG_LEVEL
   # Should output: DEBUG
   ```

2. **Verify .env file:**
   ```bash
   grep LOG_LEVEL .env
   # Should show: LOG_LEVEL=DEBUG
   ```

3. **Test with the test script:**
   ```bash
   LOG_LEVEL=DEBUG python test_debug_logging.py
   ```

### Docker Environment

For Docker deployments, ensure the LOG_LEVEL environment variable is properly passed:

**docker-compose-local.yml** (already configured):
```yaml
environment:
  - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--log-level", "debug"]
```

**docker-compose.yml** (production):
```yaml
env_file:
  - .env
command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--log-level", "info"]
```

### Common Issues

1. **Uvicorn log level mismatch**: Fixed by synchronizing uvicorn's log level with the application log level
2. **Missing timestamps**: Fixed by updating the log format
3. **Debug messages filtered**: Ensure LOG_LEVEL=DEBUG is set correctly

## Development vs Production

- **Development**: Use `LOG_LEVEL=DEBUG` for detailed debugging
- **Production**: Use `LOG_LEVEL=INFO` or `LOG_LEVEL=WARNING` to reduce log volume

## Log Locations

- **Local development**: Console output
- **Docker local**: `docker-compose logs`
- **Docker production**: AWS CloudWatch (configured in docker-compose.yml)

## Examples

### Viewing Debug Logs in Development
```bash
# Set debug level
echo "LOG_LEVEL=DEBUG" >> .env

# Run application
python run.py

# In another terminal, trigger an API call to see debug logs
curl -H "X-API-Key: your-api-key" "http://localhost:8000/asm/webservers/status/your-job-id?page=1"
```

### Viewing Debug Logs in Docker
```bash
# Set debug level
echo "LOG_LEVEL=DEBUG" >> .env

# Start with local compose file (has debug enabled)
docker-compose -f docker-compose-local.yml up --build

# View logs
docker-compose -f docker-compose-local.yml logs -f api
```
