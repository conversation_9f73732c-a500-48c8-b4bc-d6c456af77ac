#!/usr/bin/env python3
"""
Test script to verify debug logging is working correctly.
"""
import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging the same way as the main app
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=getattr(logging, log_level, logging.INFO),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

logger = logging.getLogger(__name__)

def test_logging_levels():
    """Test all logging levels to verify they work correctly."""
    print(f"Current LOG_LEVEL environment variable: {os.getenv('LOG_LEVEL', 'Not set')}")
    print(f"Effective logging level: {logging.getLevelName(logger.getEffectiveLevel())}")
    print("\nTesting different log levels:")
    print("-" * 50)
    
    logger.debug("This is a DEBUG message - should only appear when LOG_LEVEL=DEBUG")
    logger.info("This is an INFO message - should appear when LOG_LEVEL=INFO or DEBUG")
    logger.warning("This is a WARNING message - should appear at WARNING level and above")
    logger.error("This is an ERROR message - should appear at ERROR level and above")
    logger.critical("This is a CRITICAL message - should always appear")
    
    print("-" * 50)
    print("If you set LOG_LEVEL=DEBUG in your .env file, you should see the DEBUG message above.")
    print("If LOG_LEVEL=INFO (default), you should see INFO, WARNING, ERROR, and CRITICAL messages.")

if __name__ == "__main__":
    test_logging_levels()
