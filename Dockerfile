FROM nanditacurlsek/threatmesh-base:latest

# Accept GitHub PAT as build argument
ARG GITHUB_PAT
ARG SUBFINDER_PROVIDER

# Fail if GITHUB_PAT is not provided
RUN if [ -z "$GITHUB_PAT" ]; then \
        echo "ERROR: GITHUB_PAT build argument is required but not provided"; \
        echo "Usage: docker build --build-arg GITHUB_PAT=your_token_here ."; \
        exit 1; \
    fi

# Validate SUBFINDER_PROVIDER is provided and decode
RUN if [ -z "$SUBFINDER_PROVIDER" ]; then \
        echo "ERROR: SUBFINDER_PROVIDER build argument is required but not provided"; \
        echo "Usage: docker build --build-arg SUBFINDER_PROVIDER=base64_config ."; \
        exit 1; \
    fi && \
    mkdir -p /opt/configs && \
    echo "$SUBFINDER_PROVIDER" | base64 -d > /opt/configs/subfinder-provider.yaml || \
    { echo "ERROR: Failed to decode SUBFINDER_PROVIDER — ensure it is valid base64"; exit 1; }

# Set working directory
WORKDIR /app

# Install git-hound (dynamic latest nightly release)
RUN ASSET_ID=$(curl -s -H "Authorization: Bearer ${GITHUB_PAT}" \
        "https://api.github.com/repos/Curlsek/git-hound/releases/tags/nightly" | \
        jq -r '.assets[] | select(.name == "git-hound") | .id') && \
    curl -L -H "Authorization: Bearer ${GITHUB_PAT}" \
        -H "Accept: application/octet-stream" \
        "https://api.github.com/repos/Curlsek/git-hound/releases/assets/$ASSET_ID" \
        -o /opt/git-hound && \
    chmod +x /opt/git-hound

# Verify git-hound is executable and working
RUN if [ ! -x "/opt/git-hound" ]; then \
        echo "ERROR: git-hound is not executable at /opt/git-hound"; \
        exit 1; \
    fi && \
    /opt/git-hound --help > /dev/null 2>&1 || { \
        echo "ERROR: git-hound failed to execute properly"; \
        exit 1; \
    } && \
    echo "✓ git-hound successfully installed and verified"

# download wordlists on every build
RUN mkdir -p /opt/wordlists
RUN wget -O /opt/wordlists/resolvers-trusted.txt https://raw.githubusercontent.com/trickest/resolvers/refs/heads/main/resolvers-trusted.txt 

# Install Nuclei (vulnerability scanner)
RUN wget -O /tmp/nuclei.zip https://github.com/projectdiscovery/nuclei/releases/download/v3.2.8/nuclei_3.2.8_linux_amd64.zip && \
    unzip -o /tmp/nuclei.zip && \
    mv nuclei /opt/ && \
    chmod +x /opt/nuclei && \
    rm /tmp/nuclei.zip


# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 8000

# Default command (can be overridden in docker-compose)
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--log-level", "info"]